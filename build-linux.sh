#!/bin/bash

# Linux平台专用构建脚本
set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_usage() {
    echo -e "${BLUE}使用方法:${NC}"
    echo "  $0 <镜像标签>"
    echo ""
    echo -e "${BLUE}示例:${NC}"
    echo "  $0 dev250728"
    echo "  $0 v1.0.0"
    echo "  $0 latest"
    echo ""
    echo -e "${BLUE}说明:${NC}"
    echo "  镜像标签是必需的参数，用于标识Docker镜像的版本"
    echo "  镜像将构建为: registry.cn-shenzhen.aliyuncs.com/sz_tcl_ai_dockerhub/volume-exporter:<标签>"
}

# 检查参数
check_arguments() {
    if [ $# -eq 0 ]; then
        print_error "缺少镜像标签参数"
        echo ""
        print_usage
        exit 1
    fi
    
    TAG="$1"
    
    # 验证标签格式（基本检查）
    if [[ ! "$TAG" =~ ^[a-zA-Z0-9._-]+$ ]]; then
        print_error "无效的镜像标签格式: $TAG"
        print_error "标签只能包含字母、数字、点号、下划线和连字符"
        exit 1
    fi
    
    print_info "使用镜像标签: $TAG"
}

# 检查Docker服务
check_docker() {
    print_info "检查Docker服务状态..."
    
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker服务未运行"
        print_error "请启动Docker服务后重试"
        echo ""
        print_info "启动Docker的方法:"
        echo "  macOS: 打开Docker Desktop应用"
        echo "  Linux: sudo systemctl start docker"
        echo "  Windows: 打开Docker Desktop应用"
        exit 1
    fi
    
    print_info "Docker服务运行正常"
}

# 设置变量
setup_variables() {
    IMAGE_NAME="registry.cn-shenzhen.aliyuncs.com/sz_tcl_ai_dockerhub/volume-exporter"
    FULL_IMAGE_NAME="${IMAGE_NAME}:${TAG}"
    
    print_info "镜像信息:"
    echo "  仓库: $IMAGE_NAME"
    echo "  标签: $TAG"
    echo "  完整名称: $FULL_IMAGE_NAME"
    echo "  平台: linux/amd64"
}

# 构建Docker镜像
build_image() {
    print_info "开始构建Docker镜像..."
    
    # 清理旧的构建缓存（可选）
    print_info "清理构建缓存..."
    docker builder prune -f
    
    # 构建镜像
    print_info "执行Docker构建..."
    docker build \
        --platform linux/amd64 \
        --build-arg GOOS=linux \
        --build-arg GOARCH=amd64 \
        --build-arg CGO_ENABLED=0 \
        --no-cache \
        -t "${FULL_IMAGE_NAME}" \
        .
    
    if [ $? -eq 0 ]; then
        print_info "✅ Docker镜像构建成功！"
    else
        print_error "❌ Docker镜像构建失败"
        exit 1
    fi
}

# 显示镜像信息
show_image_info() {
    print_info "镜像构建完成，详细信息:"
    docker images "${FULL_IMAGE_NAME}" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
}

# 推送镜像
push_image() {
    print_info "推送镜像到仓库..."
    
    docker push "${FULL_IMAGE_NAME}"
    
    if [ $? -eq 0 ]; then
        print_info "✅ 镜像推送成功！"
        
        # 清理本地镜像
        print_info "清理本地镜像..."
        docker rmi "${FULL_IMAGE_NAME}"
        
        if [ $? -eq 0 ]; then
            print_info "✅ 本地镜像清理成功！"
        else
            print_warning "⚠️ 本地镜像清理失败，可能需要手动清理"
        fi
        
        print_info "🎉 构建和推送完成！"
        
        echo ""
        print_info "📋 使用示例:"
        echo "   docker run -d \\"
        echo "     -p 9888:9888 \\"
        echo "     -v /var/log:/host/var/log:ro \\"
        echo "     ${FULL_IMAGE_NAME} \\"
        echo "     --volume-dir=logs:/host/var/log"
        
        echo ""
        print_info "📋 容器管理示例:"
        echo "   # 启动容器"
        echo "   docker run -d --name volume-exporter \\"
        echo "     -p 9888:9888 \\"
        echo "     -v /var/log:/host/var/log:ro \\"
        echo "     ${FULL_IMAGE_NAME}"
        echo ""
        echo "   # 查看日志"
        echo "   docker logs volume-exporter"
        echo ""
        echo "   # 停止容器"
        echo "   docker stop volume-exporter"
    else
        print_error "❌ 镜像推送失败"
        print_error "请检查网络连接和镜像仓库权限"
        exit 1
    fi
}

# 询问是否推送
ask_push() {
    echo ""
    read -p "🤔 是否推送到镜像仓库? (y/N): " -n 1 -r
    echo ""
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        push_image
    else
        print_warning "镜像未推送，稍后可手动推送:"
        echo "   docker push ${FULL_IMAGE_NAME}"
    fi
}

# 主函数
main() {
    print_info "🚀 开始Docker镜像构建流程..."
    
    # 检查参数
    check_arguments "$@"
    
    # 检查Docker服务
    check_docker
    
    # 设置变量
    setup_variables
    
    # 构建镜像
    build_image
    
    # 显示镜像信息
    show_image_info
    
    # 询问是否推送
    ask_push
    
    print_info "✅ 构建流程完成"
}

# 运行主函数
main "$@" 