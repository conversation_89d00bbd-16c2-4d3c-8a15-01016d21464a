# Design Document

## Overview

This design implements configurable concurrency control for the Volume Exporter by adding a command-line parameter that controls the maximum number of concurrent goroutines used for file system operations. The design maintains backward compatibility while providing flexibility for different deployment scenarios.

## Architecture

### Current State
- Hard-coded concurrency limit of 100,000 goroutines
- Multiple semaphore declarations in different functions
- Inconsistent concurrency limits across operations

### Proposed State
- Single configurable concurrency limit
- Centralized semaphore management
- Consistent concurrency control across all operations

## Components and Interfaces

### 1. Command Line Interface
```go
// Add to main.go flag definitions
maxConcurrency = flag.Int("max-concurrency", 5000000, "Maximum number of concurrent goroutines for file operations")
```

### 2. Concurrency Manager
```go
// New structure in disk package
type ConcurrencyManager struct {
    semaphore chan struct{}
    limit     int
}

func NewConcurrencyManager(limit int) *ConcurrencyManager {
    if limit <= 0 {
        limit = 5000000 // default
    }
    return &ConcurrencyManager{
        semaphore: make(chan struct{}, limit),
        limit:     limit,
    }
}

func (cm *ConcurrencyManager) Acquire() {
    cm.semaphore <- struct{}{}
}

func (cm *ConcurrencyManager) Release() {
    <-cm.semaphore
}

func (cm *ConcurrencyManager) Limit() int {
    return cm.limit
}
```

### 3. Global Concurrency Instance
```go
// In disk package
var concurrencyManager *ConcurrencyManager

func InitConcurrency(limit int) {
    concurrencyManager = NewConcurrencyManager(limit)
    glog.Infof("Initialized concurrency manager with limit: %d", limit)
}
```

## Data Models

### Configuration Flow
1. Command line parameter parsed in `main.go`
2. Value passed to `disk.InitConcurrency()`
3. Global concurrency manager created
4. All file operations use the shared manager

### Semaphore Usage Pattern
```go
// Replace all instances of:
sem <- struct{}{}
defer func() { <-sem }()

// With:
concurrencyManager.Acquire()
defer concurrencyManager.Release()
```

## Error Handling

### Invalid Parameter Values
- Negative values: Log warning, use default
- Zero value: Log warning, use default  
- Extremely large values: Log warning, cap at reasonable maximum

### Runtime Errors
- Semaphore operations are blocking and don't return errors
- Initialization errors logged but don't prevent startup

## Testing Strategy

### Unit Tests
1. Test ConcurrencyManager creation with various limits
2. Test acquire/release operations
3. Test default value handling
4. Test invalid parameter handling

### Integration Tests
1. Test command line parameter parsing
2. Test initialization flow from main to disk package
3. Test concurrent operations respect the limit

### Performance Tests
1. Benchmark different concurrency limits
2. Test memory usage with various limits
3. Test system stability under high concurrency

## Implementation Plan

### Phase 1: Core Infrastructure
1. Add ConcurrencyManager struct to disk package
2. Add command line parameter to main.go
3. Add initialization function

### Phase 2: Replace Existing Semaphores
1. Replace semaphore in `GetSubDirInfo()`
2. Replace semaphore in `walkDir()`
3. Replace semaphore in `calculateFileSize()`

### Phase 3: Testing and Validation
1. Add unit tests for ConcurrencyManager
2. Add integration tests for parameter handling
3. Performance testing and optimization

## Migration Strategy

### Backward Compatibility
- Default value maintains reasonable performance
- No breaking changes to existing functionality
- Gradual rollout possible through configuration

### Deployment Considerations
- Document recommended values for different system sizes
- Provide guidance for tuning based on available resources
- Monitor system performance after deployment