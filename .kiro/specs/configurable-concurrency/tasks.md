# Implementation Plan

- [ ] 1. Create ConcurrencyManager structure and initialization
  - Create ConcurrencyManager struct with semaphore and limit fields
  - Implement NewConcurrencyManager constructor with validation
  - Add Acquire() and Release() methods for semaphore operations
  - Add Limit() method to return current limit
  - _Requirements: 1.3, 2.1_

- [ ] 2. Add command line parameter support
  - Add --max-concurrency flag to main.go with default value of 5,000,000
  - Parse and validate the parameter value
  - Pass the validated value to disk package initialization
  - _Requirements: 1.1, 1.2, 1.4_

- [ ] 3. Implement global concurrency manager initialization
  - Add InitConcurrency function to disk package
  - Create global concurrencyManager variable
  - Add logging for configured concurrency limit
  - Handle invalid parameter values with fallback to default
  - _Requirements: 1.4, 3.1, 3.2, 3.3_

- [ ] 4. Replace hardcoded semaphore in GetSubDirInfo function
  - Remove local semaphore declaration (line ~70)
  - Replace sem <- struct{}{} with concurrencyManager.Acquire()
  - Replace <-sem with concurrencyManager.Release()
  - Update defer statements to use Release() method
  - _Requirements: 2.1, 2.4_

- [ ] 5. Replace hardcoded semaphore in walkDir function
  - Remove global semaphore declaration (line 99)
  - Replace sem <- struct{}{} with concurrencyManager.Acquire()
  - Replace <-sem with concurrencyManager.Release()
  - Update defer statements to use Release() method
  - _Requirements: 2.2, 2.4_

- [ ] 6. Replace hardcoded semaphore in calculateFileSize function
  - Replace sem <- struct{}{} with concurrencyManager.Acquire()
  - Replace <-sem with concurrencyManager.Release()
  - Update defer statements to use Release() method
  - _Requirements: 2.3, 2.4_

- [ ] 7. Wire up initialization in main function
  - Call disk.InitConcurrency() with parsed parameter value
  - Add initialization before exporter.Register() call
  - Ensure proper error handling and logging
  - _Requirements: 1.1, 1.2, 3.1_

- [ ] 8. Add unit tests for ConcurrencyManager
  - Test NewConcurrencyManager with valid limits
  - Test NewConcurrencyManager with invalid limits (negative, zero)
  - Test Acquire() and Release() operations
  - Test Limit() method returns correct value
  - _Requirements: 1.4, 2.1_

- [ ] 9. Add integration tests for parameter handling
  - Test command line parameter parsing
  - Test initialization flow from main to disk package
  - Test default value usage when parameter not provided
  - Test error handling for invalid parameter values
  - _Requirements: 1.1, 1.2, 1.4, 3.3_