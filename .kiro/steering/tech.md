# 技术栈

## 语言和运行时
- **Go 1.21+** - 主要编程语言
- 跨平台支持（Linux、Windows、macOS）

## 核心依赖
- **Prometheus 客户端库** (`github.com/prometheus/client_golang`) - 指标收集和导出
- **glog** (`github.com/golang/glog`) - 结构化日志记录
- **YAML v3** (`gopkg.in/yaml.v3`) - 配置解析
- **Lumberjack** (`gopkg.in/natefinch/lumberjack.v2`) - 日志轮转
- **golang.org/x/sys** - 系统级操作

## 构建系统
- **Makefile** - 主要构建编排
- **Promu** - Prometheus 项目构建工具，用于交叉编译
- **Docker** - 多阶段构建的容器化

## 常用命令

### 开发
```bash
# 格式化代码
make format

# 运行测试
make test

# 代码检查
make vet

# 本地构建
make build
```

### 构建和发布
```bash
# 构建二进制文件
go build -o volume_exporter main.go

# 跨平台构建
make crossbuild

# 构建 Docker 镜像
make docker

# 创建发布压缩包
make tarballs
```

### 测试和运行
```bash
# 本地运行
go run main.go --volume-dir=logs:/var/log

# 使用 Docker 运行
docker run -p 9888:9888 -v /var/log:/host/logs:ro volume_exporter --volume-dir=logs:/host/logs

# 运行测试
go test ./...
```

## 构建配置
- **禁用 CGO** 用于静态二进制文件
- **netgo 标签** 用于纯 Go 网络
- **ldflags** 用于版本注入
- **多平台** Docker 构建（linux/amd64）

## 部署模式
- Kubernetes Pod 中的**边车容器**
- **独立 Docker** 容器
- 主机系统上的**二进制部署**