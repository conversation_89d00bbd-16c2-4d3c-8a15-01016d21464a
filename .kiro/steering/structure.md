# 项目结构

## 根目录
- `main.go` - 应用程序入口点和 CLI 参数解析
- `go.mod/go.sum` - Go 模块依赖
- `Makefile` - 构建自动化和常见任务
- `Dockerfile` - 容器镜像定义
- `config.yaml` - 运行时配置文件
- `.promu.yml` - Prometheus 构建工具配置

## 核心包

### `/config`
配置管理和日志设置
- `config.go` - YAML 配置解析和全局配置
- `log.go` - 日志初始化和设置

### `/disk`
底层磁盘操作和系统调用
- `disk.go` - 核心磁盘信息结构和接口
- `disk_test.go` - 磁盘操作单元测试
- `stat_linux.go` - Linux 特定的磁盘统计
- `stat_windows.go` - Windows 特定的磁盘统计
- `type_linux.go` - Linux 文件系统类型检测
- `type_windows.go` - Windows 文件系统类型检测

### `/exporter`
Prometheus 指标收集和导出
- `volume_exporter.go` - 主要 Prometheus 收集器实现
- `volume_exporter_test.go` - 导出器单元测试
- `export_test.go` - 导出功能测试

## 构建和部署
- `build.sh` - 带中文本地化的 Docker 构建脚本
- `build-linux.sh` - Linux 特定构建脚本
- `run-container.sh` - 容器执行助手

## 测试脚本
- `test-*.sh` - 各种测试场景（并发、性能、HTTP 服务器等）

## 文档
- `/docs/img/` - 截图和文档图片
- `README.md` - 主要项目文档
- `CONFIG_EXAMPLE.md` - 配置示例
- `DOCKER_BUILD.md` - Docker 构建说明

## 代码组织模式

### 包职责
- **main** - CLI 解析、应用程序引导、HTTP 服务器设置
- **config** - 配置管理、日志记录、全局状态
- **disk** - 平台特定的磁盘操作、文件系统查询
- **exporter** - Prometheus 指标收集、收集器注册

### 文件命名约定
- `*_test.go` - 单元测试文件
- `*_linux.go` - Linux 特定实现
- `*_windows.go` - Windows 特定实现
- `test-*.sh` - 测试自动化脚本

### 关键架构模式
- **收集器模式** - Prometheus 收集器接口实现
- **平台抽象** - 操作系统特定文件分离
- **配置注入** - 全局配置模式
- **基于标志的 CLI** - 标准 Go flag 包使用