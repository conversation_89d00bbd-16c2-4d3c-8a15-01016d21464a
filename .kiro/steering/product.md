# 卷导出器 (Volume Exporter)

卷导出器是一个 Prometheus 导出器，用于在传统节点导出器无法部署的容器化环境中监控磁盘/卷/PV 存储。

## 产品目标

- 在主机路径挂载受限的环境中监控磁盘使用情况
- 在未授予集群角色的多租户集群中提供卷指标
- 为 Kubernetes Pod 中的卷监控提供轻量级边车解决方案
- 填补节点导出器无法部署时的监控空白

## 核心特性

- 支持多卷配置的可配置卷监控
- Prometheus 指标导出（总计、空闲、已用字节数和百分比）
- 轻量级 Docker 镜像（6.84MB）
- 低 CPU 和内存消耗
- 支持边车部署模式
- 跨平台支持（Linux/Windows）

## 导出的指标

- `volume_bytes_total`: 卷/磁盘的总大小
- `volume_bytes_free`: 卷/磁盘的空闲大小
- `volume_bytes_used`: 卷/磁盘的已用大小
- `volume_percentage_used`: 卷/磁盘利用率百分比
- `volume_exporter_build_info`: 构建信息指标

## 目标使用场景

- 主机访问受限的企业环境
- 多租户 Kubernetes 集群
- PV 监控受限的云环境
- 需要卷监控的容器化应用程序