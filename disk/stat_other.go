//go:build !linux
// +build !linux

package disk

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"

	"github.com/golang/glog"
)

// GetInfo returns total and free bytes available in a directory for non-Linux platforms
// This is a stub implementation for development/testing purposes
func GetInfo(path string) (info Info, err error) {
	// 获取基本的文件系统信息（简化版本）
	stat, err := os.Stat(path)
	if err != nil {
		return Info{}, err
	}

	if !stat.IsDir() {
		return Info{}, fmt.Errorf("path %s is not a directory", path)
	}

	// 对于非Linux平台，提供一个基本的实现
	// 注意：这只是为了开发和测试目的，实际生产环境应该使用Linux
	info = Info{
		Total:     1000000000, // 1GB 作为示例
		Free:      500000000,  // 500MB 作为示例
		Files:     1000,       // 示例值
		Ffree:     500,        // 示例值
		FSType:    "UNKNOWN",
		Path:      path,
	}

	info.UID, info.Namespace, info.WorkLoad = processDirName(filepath.Base(path))

	// 计算已使用空间（简化版本）
	var totalSize int64
	err = filepath.Walk(path, func(filePath string, fileInfo os.FileInfo, err error) error {
		if err != nil {
			return nil // 忽略错误，继续处理
		}
		if !fileInfo.IsDir() {
			totalSize += fileInfo.Size()
		}
		return nil
	})

	if err != nil {
		glog.Warningf("Error walking directory %s: %v", path, err)
	}

	info.Used = uint64(totalSize)

	return info, nil
}

// GetSubDirInfo returns information about subdirectories for non-Linux platforms
// This is a stub implementation for development/testing purposes
func GetSubDirInfo(directory string, excludeDirs []string) (*sync.Map, error) {
	// 将排除目录转换为map以便快速查找
	excludeMap := make(map[string]bool)
	for _, dir := range excludeDirs {
		excludeMap[dir] = true
	}

	infoMap := &sync.Map{}
	var wg sync.WaitGroup

	files, err := os.ReadDir(directory)
	if err != nil {
		return nil, err
	}

	for _, file := range files {
		if !file.IsDir() {
			continue
		}

		// 检查是否在排除列表中
		if excludeMap[file.Name()] {
			continue
		}

		wg.Add(1)
		go func(fileName string) {
			defer wg.Done()                    // 任务完成
			concurrencyManager.Acquire()       // 获取信号量
			defer concurrencyManager.Release() // 释放信号量

			filePath := filepath.Join(directory, fileName)
			subInfo, err := GetInfo(filePath)
			if err != nil {
				glog.Infoln("Error getting info for", filePath, ":", err)
				return
			}

			// 存储结果到 infoMap
			infoMap.Store(subInfo.UID, subInfo)
		}(file.Name())
	}

	wg.Wait() // 等待所有任务完成

	return infoMap, nil
}

// 分割目录名，获取uid，namespace，workload
func processDirName(dirName string) (uid, namespace, workload string) {
	parts := strings.SplitN(dirName, "-pvc-", 2)
	// 如果没有 "-pvc-"，则直接notknown
	if len(parts) < 2 {
		glog.Infoln("The dir name do not contain '-pvc-' return notknown")
		return "notknown", "notknown", "notknown"
	}
	// "pvc-" + 第二部分就是第三个子字符串
	uid = parts[1]
	if strings.Contains(parts[0], "monitoring") {
		part := strings.Split(parts[0], "-")
		namespace = part[0]
		workload = strings.Join(part[1:], "-")
		return uid, namespace, workload
	}

	// 将第一部分按照 "-" 分割，然后找到中间的 "-" 的位置
	parts = strings.Split(parts[0], "-")

	middleIndex := len(parts) / 2
	namespace = strings.Join(parts[:middleIndex], "-")
	workload = strings.Join(parts[middleIndex:], "-")

	return uid, namespace, workload
}
