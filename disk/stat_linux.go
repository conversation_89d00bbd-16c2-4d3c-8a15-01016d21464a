//go:build linux
// +build linux

package disk

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"syscall"

	"github.com/golang/glog"
)

//var (
//	//infoMap map[string]Info
//
//	// 限制最大并发数
//	sem = make(chan struct{}, 1000000)
//)

// GetInfo returns total and free bytes available in a directory, e.g. `/`.
func GetInfo(path string) (info Info, err error) {
	// dirSie, err := directorySize(path)
	// if err != nil {
	// 	return Info{}, err
	// }
	// 判断map中是否有该目录的大小
	// dirSie, ok := DirSizes[filepath.Base(path)]
	// if !ok {
	// 	log.Printf("Path: %s does not contain the key", filepath.Base(path))
	// }

	s := syscall.Statfs_t{}
	err = syscall.Statfs(path, &s)
	if err != nil {
		return Info{}, err
	}
	reservedBlocks := s.Bfree - s.Bavail
	info = Info{
		Total:  uint64(s.Frsize) * (s.Blocks - reservedBlocks),
		Free:   uint64(s.Frsize) * s.Bavail,
		Files:  s.Files,
		Ffree:  s.Ffree,
		FSType: getFSType(s.Type),
		Path:   path,
	}
	//info.Path = filepath.Base(path)

	info.UID, info.Namespace, info.WorkLoad = processDirName(filepath.Base(path))

	// XFS can show wrong values at times error out
	// in such scenarios.
	if info.Free > info.Total {
		return info, fmt.Errorf("detected free space (%d) > total disk space (%d), fs corruption at (%s). please run 'fsck'", info.Free, info.Total, path)
	}
	// info.Used = info.Total - info.Free
	// info.Used = uint64(dirSie)

	//DirSizes.Range(func(key, value interface{}) bool {
	//	parts := strings.Split(key.(string), "/")
	//	if filepath.Base(path) == parts[2] {
	//		info.Used += uint64(value.(int64))
	//	}
	//	return true
	//})

	// 使用全局并发管理器的限制作为缓冲区大小，但设置一个合理的上限
	bufferSize := concurrencyManager.Limit()

	fileSizeChan := make(chan int64, bufferSize)
	var wg sync.WaitGroup

	wg.Add(1)
	go walkDir(path, &wg, fileSizeChan)

	go func() {
		wg.Wait()
		close(fileSizeChan)
	}()

	var totalSize int64
	for size := range fileSizeChan {
		totalSize += size
	}
	info.Used = uint64(totalSize)

	return info, nil
}

func GetSubDirInfo(directory string, excludeDirs []string) (*sync.Map, error) {
	// 将排除目录转换为map以便快速查找
	excludeMap := make(map[string]bool)
	for _, dir := range excludeDirs {
		excludeMap[dir] = true
	}

	infoMap := &sync.Map{}
	var wg sync.WaitGroup
	files, err := os.ReadDir(directory)
	if err != nil {
		return nil, err
	}

	for _, file := range files {
		if !file.IsDir() {
			continue
		}

		// 检查是否在排除列表中
		if excludeMap[file.Name()] {
			continue
		}

		wg.Add(1)
		go func(fileName string) {
			defer wg.Done()                    // 任务完成
			concurrencyManager.Acquire()       // 获取信号量
			defer concurrencyManager.Release() // 释放信号量

			filePath := filepath.Join(directory, fileName)
			subInfo, err := GetInfo(filePath)
			if err != nil {
				glog.Infoln("Error getting info for", filePath, ":", err)
				return
			}

			// 存储结果到 infoMap
			infoMap.Store(subInfo.UID, subInfo)
		}(file.Name())
	}

	wg.Wait() // 等待所有任务完成

	return infoMap, nil
}

// 分割目录名，获取uid，namespace，workload
func processDirName(dirName string) (uid, namespace, workload string) {
	parts := strings.SplitN(dirName, "-pvc-", 2)
	// 如果没有 "-pvc-"，则直接notknown
	if len(parts) < 2 {
		glog.Infoln("The dir name do not contain '-pvc-' return notknown")
		return "notknown", "notknown", "notknown"
	}
	// "pvc-" + 第二部分就是第三个子字符串
	uid = parts[1]
	if strings.Contains(parts[0], "monitoring") {
		part := strings.Split(parts[0], "-")
		namespace = part[0]
		workload = strings.Join(part[1:], "-")
		return uid, namespace, workload
	}

	// 将第一部分按照 "-" 分割，然后找到中间的 "-" 的位置
	parts = strings.Split(parts[0], "-")

	middleIndex := len(parts) / 2
	namespace = strings.Join(parts[:middleIndex], "-")
	workload = strings.Join(parts[middleIndex:], "-")

	return uid, namespace, workload
}

func walkDir(dirPath string, wg *sync.WaitGroup, fileSizeChan chan<- int64) {
	defer wg.Done()

	concurrencyManager.Acquire()       // 获取信号量
	defer concurrencyManager.Release() // 释放信号量

	entries, err := os.ReadDir(dirPath)
	if err != nil {
		glog.Infoln("Error reading directory:", err)
		return
	}

	var subWg sync.WaitGroup
	for _, entry := range entries {
		path := filepath.Join(dirPath, entry.Name())
		if entry.IsDir() {
			subWg.Add(1)
			go walkDir(path, &subWg, fileSizeChan) // 递归调用
		} else {
			subWg.Add(1)
			go func(path string) {
				defer subWg.Done()
				calculateFileSize(path, fileSizeChan)
			}(path)
		}
	}

	subWg.Wait() // 等待所有子任务完成
}

func calculateFileSize(filePath string, fileSizeChan chan<- int64) {
	concurrencyManager.Acquire()       // 获取信号量
	defer concurrencyManager.Release() // 释放信号量

	fileInfo, err := os.Stat(filePath)
	if err != nil {
		glog.Infoln("Error getting file info:", err)
		fileSizeChan <- 0
		return
	}
	fileSizeChan <- fileInfo.Size()
}
