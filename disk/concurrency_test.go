package disk

import (
	"testing"
	"time"
)

func TestNewConcurrencyManager(t *testing.T) {
	// 测试正常情况
	cm := NewConcurrencyManager(10)
	if cm.Limit() != 10 {
		t.Errorf("期望限制为10，实际得到%d", cm.Limit())
	}

	// 测试无效值情况
	cm = NewConcurrencyManager(0)
	if cm.Limit() != 5000000 {
		t.<PERSON>rrorf("期望默认限制为5000000，实际得到%d", cm.Limit())
	}

	cm = NewConcurrencyManager(-5)
	if cm.Limit() != 5000000 {
		t.Errorf("期望默认限制为5000000，实际得到%d", cm.Limit())
	}
}

func TestConcurrencyManagerAcquireRelease(t *testing.T) {
	cm := NewConcurrencyManager(2)

	// 获取两个信号量
	cm.Acquire()
	cm.Acquire()

	// 第三个应该阻塞，所以我们在goroutine中测试
	done := make(chan bool)
	go func() {
		cm.Acquire()
		done <- true
	}()

	// 等待一小段时间确保goroutine被阻塞
	select {
	case <-done:
		t.Error("第三个Acquire()应该被阻塞")
	case <-time.After(100 * time.Millisecond):
		// 预期行为
	}

	// 释放一个信号量
	cm.Release()

	// 现在第三个Acquire()应该能够继续
	select {
	case <-done:
		// 预期行为
	case <-time.After(100 * time.Millisecond):
		t.Error("释放后Acquire()应该能够继续")
	}

	// 清理
	cm.Release()
	cm.Release()
}

func TestInitConcurrency(t *testing.T) {
	// 测试初始化
	InitConcurrency(100)

	if concurrencyManager == nil {
		t.Error("全局并发管理器应该被初始化")
	}

	if concurrencyManager.Limit() != 100 {
		t.Errorf("期望限制为100，实际得到%d", concurrencyManager.Limit())
	}
}
