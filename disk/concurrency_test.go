package disk

import (
	"testing"
	"time"
)

func TestInitConcurrency(t *testing.T) {
	// 测试初始化并发管理器
	InitConcurrency(1000)

	if MaxConcurrency != 1000 {
		t.Errorf("期望并发限制为1000，实际得到%d", MaxConcurrency)
	}

	if globalSemaphore == nil {
		t.<PERSON><PERSON>("全局信号量应该被初始化")
	}
}

func TestSemaphoreAcquireRelease(t *testing.T) {
	// 初始化一个小的信号量用于测试
	InitConcurrency(2)

	// 获取两个信号量
	AcquireSemaphore()
	AcquireSemaphore()

	// 第三个应该阻塞，所以我们在goroutine中测试
	done := make(chan bool)
	go func() {
		AcquireSemaphore()
		done <- true
	}()

	// 等待一小段时间确保goroutine被阻塞
	select {
	case <-done:
		t.Error("第三个AcquireSemaphore()应该被阻塞")
	case <-time.After(100 * time.Millisecond):
		// 预期行为
	}

	// 释放一个信号量
	ReleaseSemaphore()

	// 现在第三个AcquireSemaphore()应该能够继续
	select {
	case <-done:
		// 预期行为
	case <-time.After(100 * time.Millisecond):
		t.Error("释放后AcquireSemaphore()应该能够继续")
	}

	// 清理
	ReleaseSemaphore()
	ReleaseSemaphore()
}
