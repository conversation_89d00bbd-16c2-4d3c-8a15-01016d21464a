package disk

// Info stat fs struct is container which holds following values
// Total - total size of the volume / disk
// Free - free size of the volume / disk
// Files - total inodes available
// Ffree - free inodes available
// FSType - file system type
type Info struct {
	Total     uint64
	Free      uint64
	Used      uint64
	Files     uint64
	Ffree     uint64
	FSType    string
	Path      string
	Namespace string
	WorkLoad  string
	UID       string
}

// 移除所有并发限制以提高性能，避免指标抓取超时
// 原有的ConcurrencyManager已被移除，不再使用信号量限制
