package disk

import (
	"github.com/golang/glog"
)

// Info stat fs struct is container which holds following values
// Total - total size of the volume / disk
// Free - free size of the volume / disk
// Files - total inodes available
// Ffree - free inodes available
// FSType - file system type
type Info struct {
	Total     uint64
	Free      uint64
	Used      uint64
	Files     uint64
	Ffree     uint64
	FSType    string
	Path      string
	Namespace string
	WorkLoad  string
	UID       string
}

// ConcurrencyManager manages concurrent goroutines using a semaphore
type ConcurrencyManager struct {
	semaphore chan struct{}
	limit     int
}

// Global concurrency manager instance
var concurrencyManager *ConcurrencyManager

// NewConcurrencyManager creates a new ConcurrencyManager with the specified limit
func NewConcurrencyManager(limit int) *ConcurrencyManager {
	if limit <= 0 {
		glog.Warningf("Invalid concurrency limit %d, using default value 5000000", limit)
		limit = 5000000
	}
	return &ConcurrencyManager{
		semaphore: make(chan struct{}, limit),
		limit:     limit,
	}
}

// Acquire acquires a semaphore slot, blocking if necessary
func (cm *ConcurrencyManager) Acquire() {
	cm.semaphore <- struct{}{}
}

// Release releases a semaphore slot
func (cm *ConcurrencyManager) Release() {
	<-cm.semaphore
}

// Limit returns the current concurrency limit
func (cm *ConcurrencyManager) Limit() int {
	return cm.limit
}

// InitConcurrency initializes the global concurrency manager
func InitConcurrency(limit int) {
	concurrencyManager = NewConcurrencyManager(limit)
	glog.Infof("Initialized concurrency manager with limit: %d", concurrencyManager.Limit())
}
