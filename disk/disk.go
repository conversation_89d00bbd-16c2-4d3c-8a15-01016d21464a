package disk

import (
	"github.com/golang/glog"
)

// Info stat fs struct is container which holds following values
// Total - total size of the volume / disk
// Free - free size of the volume / disk
// Files - total inodes available
// Ffree - free inodes available
// FSType - file system type
type Info struct {
	Total     uint64
	Free      uint64
	Used      uint64
	Files     uint64
	Ffree     uint64
	FSType    string
	Path      string
	Namespace string
	WorkLoad  string
	UID       string
}

// 全局并发限制变量，默认500万
var MaxConcurrency int = 5000000

// 全局信号量，用于控制并发数量
var globalSemaphore chan struct{}

// InitConcurrency 初始化全局并发控制
func InitConcurrency(maxConcurrency int) {
	MaxConcurrency = maxConcurrency
	globalSemaphore = make(chan struct{}, MaxConcurrency)
	glog.Infof("Initialized global semaphore with limit: %d", MaxConcurrency)
}

// AcquireSemaphore 获取信号量
func AcquireSemaphore() {
	if globalSemaphore != nil {
		globalSemaphore <- struct{}{}
	}
}

// ReleaseSemaphore 释放信号量
func ReleaseSemaphore() {
	if globalSemaphore != nil {
		<-globalSemaphore
	}
}
