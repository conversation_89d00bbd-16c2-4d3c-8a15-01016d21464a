# 线程耗尽错误修复文档

## 问题描述
程序出现 `runtime: program exceeds 10000-thread limit` 和 `fatal error: thread exhaustion` 错误。

## 根本原因
在高并发文件系统操作中，当文件或目录被删除时，goroutine可能会发生panic或异常退出，导致：
1. 信号量没有正确释放
2. WaitGroup计数器没有正确递减
3. 大量goroutine堆积，最终导致线程耗尽

## 修复方案

### 修复点1: 目录读取失败处理
**位置**: `walkDir` 函数中的 `os.ReadDir(dirPath)`
**问题**: 当目录被清理删除时，读取失败但没有适当的错误处理
**修复**: 
```go
entries, err := os.ReadDir(dirPath)
if err != nil {
    // 修复点1: 当读取目录失败时(可能被清理删除了)直接跳过这个目录
    glog.Infoln("Error reading directory (directory may have been deleted):", err)
    return
}
```

### 修复点2: 文件读取失败处理
**位置**: `calculateFileSize` 函数中的 `os.Stat(filePath)`
**问题**: 当文件被清理删除时，读取失败但channel没有接收到数据
**修复**: 
```go
fileInfo, err := os.Stat(filePath)
if err != nil {
    // 修复点2: 当读取某个文件失败时(可能被清理删除了)执行fileSizeChan <- 0然后跳过
    glog.Infoln("Error getting file info (file may have been deleted):", err)
    fileSizeChan <- 0
    return
}
```

### 修复点3: Goroutine异常安全处理
**位置**: 所有goroutine函数
**问题**: 当goroutine发生panic时，defer语句可能不会执行，导致资源泄漏
**修复**: 在所有goroutine中添加panic恢复机制

#### 3.1 GetSubDirInfo中的goroutine
```go
go func(fileName string) {
    // 修复点3: 确保不管异常还是正常执行，go线程都进行线程释放
    defer func() {
        wg.Done() // 任务完成
        // 使用recover确保即使发生panic也能释放信号量
        if r := recover(); r != nil {
            glog.Errorf("Panic in GetSubDirInfo goroutine: %v", r)
        }
    }()
    
    concurrencyManager.Acquire()       // 获取信号量
    defer concurrencyManager.Release() // 释放信号量
    // ... 业务逻辑
}(file.Name())
```

#### 3.2 walkDir函数
```go
func walkDir(dirPath string, wg *sync.WaitGroup, fileSizeChan chan<- int64) {
    // 修复点3: 确保不管异常还是正常执行，go线程都进行线程释放
    defer func() {
        wg.Done()
        // 使用recover确保即使发生panic也能释放信号量
        if r := recover(); r != nil {
            glog.Errorf("Panic in walkDir goroutine: %v", r)
        }
    }()
    // ... 业务逻辑
}
```

#### 3.3 文件处理goroutine
```go
go func(path string) {
    // 修复点3: 确保文件处理goroutine也有panic恢复
    defer func() {
        subWg.Done()
        if r := recover(); r != nil {
            glog.Errorf("Panic in file processing goroutine: %v", r)
        }
    }()
    calculateFileSize(path, fileSizeChan)
}(path)
```

#### 3.4 calculateFileSize函数
```go
func calculateFileSize(filePath string, fileSizeChan chan<- int64) {
    concurrencyManager.Acquire() // 获取信号量
    // 修复点3: 确保不管异常还是正常执行，go线程都进行线程释放
    defer func() {
        concurrencyManager.Release() // 释放信号量
        // 使用recover确保即使发生panic也能释放信号量
        if r := recover(); r != nil {
            glog.Errorf("Panic in calculateFileSize: %v", r)
            // 即使发生panic也要发送0到channel，避免阻塞
            select {
            case fileSizeChan <- 0:
            default:
                // channel可能已关闭，忽略
            }
        }
    }()
    // ... 业务逻辑
}
```

## 修复的文件
1. `disk/stat_linux.go` - Linux平台的实现
2. `disk/stat_other.go` - 其他平台的实现

## 修复效果
1. **防止资源泄漏**: 确保所有goroutine都能正确释放信号量和WaitGroup计数
2. **提高容错性**: 当文件/目录被删除时，程序能够优雅地处理错误
3. **避免线程耗尽**: 通过proper cleanup防止goroutine堆积
4. **保持数据一致性**: 即使在异常情况下也能向channel发送数据，避免阻塞

## 验证方法
1. 编译检查: `go build .` - 成功
2. 单元测试: `go test ./disk -v` - 并发管理器测试通过
3. 在高并发环境下运行程序，观察是否还会出现线程耗尽错误

## 注意事项
- 所有的panic恢复都会记录错误日志，便于调试
- 在channel操作中使用了select语句防止阻塞
- 保持了原有的业务逻辑不变，只是增加了错误处理和资源清理
