package exporter

import (
	"github.com/golang/glog"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/collectors"
	"github.com/prometheus/common/version"

	"github.com/mnadeem/volume_exporter/disk"
)

const (
	namespace = "volume" // for Prometheus metrics.
)

// VolumeOpts is for options
type VolumeOpts struct {
	Options []VolumeOpt
}

// VolumeOpt is for option
type VolumeOpt struct {
	Name        string
	Path        string
	ExcludeDirs []string
}

// Define a struct for you collector that contains pointers
// to prometheus descriptors for each metric you wish to expose.
// Note you can also include fields of other types if they provide utility
// but we just won't be exposing them as metrics.
type volumeCollector struct {
	volumeBytesTotal *prometheus.Desc
	volumeBytesFree  *prometheus.Desc
	volumeBytesUsed  *prometheus.Desc
	volumePrcntUsed  *prometheus.Desc

	volOptions VolumeOpts
}

// You must create a constructor for you collector that
// initializes every descriptor and returns a pointer to the collector
func newVolumeCollector(opts *VolumeOpts) *volumeCollector {
	return &volumeCollector{
		volumeBytesTotal: prometheus.NewDesc(prometheus.BuildFQName(namespace, "", "bytes_total"),
			"Total size of the volume/disk",
			[]string{"cluster", "volume_path", "namespace", "workload", "uid"}, nil,
		),
		volumeBytesFree: prometheus.NewDesc(prometheus.BuildFQName(namespace, "", "bytes_free"),
			"Free size of the volume/disk",
			[]string{"cluster", "volume_path", "namespace", "workload", "uid"}, nil,
		),
		volumeBytesUsed: prometheus.NewDesc(prometheus.BuildFQName(namespace, "", "bytes_used"),
			"Used size of volume/disk",
			[]string{"cluster", "volume_path", "namespace", "workload", "uid"}, nil,
		),
		volumePrcntUsed: prometheus.NewDesc(prometheus.BuildFQName(namespace, "", "percentage_used"),
			"Percentage of volume/disk Utilization",
			[]string{"cluster", "volume_path", "namespace", "workload", "uid"}, nil,
		),

		volOptions: *opts,
	}
}

// Each and every collector must implement the Describe function.
// It essentially writes all descriptors to the prometheus desc channel.
func (collector *volumeCollector) Describe(ch chan<- *prometheus.Desc) {

	// Update this section with the each metric you create for a given collector
	ch <- collector.volumeBytesTotal
	ch <- collector.volumeBytesFree
	ch <- collector.volumeBytesUsed
	ch <- collector.volumePrcntUsed
}

// Collect implements required collect function for all promehteus collectors
func (collector *volumeCollector) Collect(ch chan<- prometheus.Metric) {
	for _, opt := range collector.volOptions.Options {
		fileInfo, err := disk.GetSubDirInfo(opt.Path, opt.ExcludeDirs)
		if err != nil {
			glog.Fatalln(err)
		}

		fileInfo.Range(func(key, value interface{}) bool {
			di := value.(disk.Info)

			percentage := (float64(di.Used) / float64(di.Total)) * 100

			ch <- prometheus.MustNewConstMetric(collector.volumeBytesTotal, prometheus.GaugeValue, float64(di.Total),
				opt.Name, di.Path, di.Namespace, di.WorkLoad, di.UID)
			ch <- prometheus.MustNewConstMetric(collector.volumeBytesFree, prometheus.GaugeValue, float64(di.Free),
				opt.Name, di.Path, di.Namespace, di.WorkLoad, di.UID)
			ch <- prometheus.MustNewConstMetric(collector.volumeBytesUsed, prometheus.GaugeValue, float64(di.Used),
				opt.Name, di.Path, di.Namespace, di.WorkLoad, di.UID)
			ch <- prometheus.MustNewConstMetric(collector.volumePrcntUsed, prometheus.GaugeValue, percentage, opt.Name,
				di.Path, di.Namespace, di.WorkLoad, di.UID)
			return true
		})
	}
}

// Register registers the volume metrics
func Register(options *VolumeOpts) {
	collector := newVolumeCollector(options)
	prometheus.MustRegister(version.NewCollector("volume_exporter"))
	prometheus.MustRegister(collector)
	//prometheus.Unregister(prometheus.NewGoCollector())
	prometheus.Unregister(collectors.NewGoCollector())
}
