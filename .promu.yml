go:
    cgo: false
repository:
    path: github.com/mnadeem/volume_exporter
build:
    flags: -a -tags netgo
    ldflags: |
        -X {{repoPath}}/vendor/github.com/prometheus/common/version.Version={{.Version}}
        -X {{repoPath}}/vendor/github.com/prometheus/common/version.Revision={{.Revision}}
        -X {{repoPath}}/vendor/github.com/prometheus/common/version.Branch={{.Branch}}
        -X {{repoPath}}/vendor/github.com/prometheus/common/version.BuildUser={{user}}@{{host}}
        -X {{repoPath}}/vendor/github.com/prometheus/common/version.BuildDate={{date "20060102-15:04:05"}}
    prefix: .
    binaries:
        - name: volume_exporter
          path: .
tarball:
    files:
        - LICENSE
        - README.md
