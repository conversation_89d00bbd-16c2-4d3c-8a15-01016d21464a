# Volume Exporter 项目架构文档

## 概述

Volume Exporter 是一个基于 **分层架构** 和 **Prometheus 收集器模式** 的 Go 应用程序，专门用于监控容器化环境中的卷存储使用情况。该项目填补了传统节点导出器无法部署时的监控空白。

## 整体架构设计

### 核心架构层次

```
┌─────────────────────────────────────────┐
│              HTTP 服务层                │  ← main.go
├─────────────────────────────────────────┤
│           Prometheus 导出层             │  ← exporter/
├─────────────────────────────────────────┤
│            磁盘操作抽象层               │  ← disk/
├─────────────────────────────────────────┤
│          平台特定实现层                 │  ← stat_*.go, type_*.go
├─────────────────────────────────────────┤
│            配置和日志层                 │  ← config/
└─────────────────────────────────────────┘
```

### 架构特点

- **分层设计**：清晰的职责分离，每层专注特定功能
- **平台抽象**：支持 Linux 和 Windows 平台的统一接口
- **并发优化**：大量使用 Go 协程和信号量控制
- **云原生**：专为 Kubernetes 环境设计的标签和元数据解析

## 详细执行流程

### 1. 应用启动阶段

**文件：** `main.go`

```go
// 应用程序入口点，直接进入主逻辑
func main() {
    // 解析 --volume-dir 参数 (格式: name:path)
    // 解析 --exclude-subdirs 参数
    // 解析 HTTP 服务配置
}
```

**关键步骤：**

1. **参数解析**：解析 `--volume-dir=logs:/var/log` 格式的参数
2. **路径验证**：验证指定路径的存在性和可访问性
3. **排除处理**：处理 `--exclude-subdirs` 排除目录列表
4. **配置构建**：构建 `VolumeOpts` 配置结构体

**注意：** 日志配置现在统一在 `config` 包中处理，移除了 `main.go` 中的冗余初始化函数，简化了启动流程。

### 2. 收集器注册阶段

**文件：** `exporter/volume_exporter.go`

```go
// 创建 Prometheus 收集器
func Register(options *VolumeOpts) {
    collector := newVolumeCollector(options)
    prometheus.MustRegister(version.NewCollector("volume_exporter"))
    prometheus.MustRegister(collector)
}
```

**收集器设计模式：**

- 实现 `prometheus.Collector` 接口的 `Describe()` 和 `Collect()` 方法
- 定义四个核心指标：`bytes_total`, `bytes_free`, `bytes_used`, `percentage_used`
- 每个指标包含丰富的标签维度：`cluster`, `volume_path`, `namespace`, `workload`, `uid`

### 3. HTTP 服务启动

**文件：** `main.go`

```go
func serverMetrics(listenAddress, metricsPath string) error {
    // 注册 /metrics 端点
    http.Handle(metricsPath, promhttp.Handler())
    
    // 注册根路径的 HTML 页面
    http.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
        // 返回简单的 HTML 页面，包含指向 /metrics 的链接
    })
    
    return http.ListenAndServe(listenAddress, nil)
}
```

**服务特点：**

- 默认监听端口：`:9888`
- 指标端点：`/metrics`
- 根路径提供简单的 Web 界面

### 4. 指标收集流程（核心业务逻辑）

当 Prometheus 访问 `/metrics` 端点时，触发以下完整流程：

#### 4.1 收集器调用链

```go
// 1. Prometheus 调用 Collect 方法
func (collector *volumeCollector) Collect(ch chan<- prometheus.Metric) {
    for _, opt := range collector.volOptions.Options {
        // 2. 获取子目录信息
        fileInfo, err := disk.GetSubDirInfo(opt.Path, opt.ExcludeDirs)
        
        // 3. 遍历每个子目录的统计信息
        fileInfo.Range(func(key, value interface{}) bool {
            di := value.(disk.Info)
            // 4. 计算使用率百分比
            percentage := (float64(di.Used) / float64(di.Total)) * 100
            
            // 5. 发送指标到 Prometheus
            ch <- prometheus.MustNewConstMetric(...)
            return true
        })
    }
}
```

#### 4.2 磁盘信息获取

**文件：** `disk/stat_linux.go`

```go
func GetSubDirInfo(directory string, excludeDirs []string) (*sync.Map, error) {
    // 1. 创建排除目录映射表
    excludeMap := make(map[string]bool)
    for _, dir := range excludeDirs {
        excludeMap[dir] = true
    }
    
    // 2. 并发处理每个子目录
    for _, file := range files {
        if !file.IsDir() || excludeMap[file.Name()] {
            continue
        }
        
        go func(fileName string) {
            // 3. 获取单个目录的磁盘信息
            subInfo, err := GetInfo(filePath)
            
            // 4. 存储到并发安全的 sync.Map
            infoMap.Store(subInfo.UID, subInfo)
        }(file.Name())
    }
}
```

#### 4.3 系统调用层（平台特定）

**Linux 实现：**

```go
func GetInfo(path string) (info Info, err error) {
    // 1. 使用 syscall.Statfs 获取文件系统统计
    s := syscall.Statfs_t{}
    err = syscall.Statfs(path, &s)
    
    // 2. 计算总空间、可用空间
    info = Info{
        Total: uint64(s.Frsize) * (s.Blocks - reservedBlocks),
        Free:  uint64(s.Frsize) * s.Bavail,
        Files: s.Files,
        Ffree: s.Ffree,
        FSType: getFSType(s.Type),
        Path: path,
    }
    
    // 3. 并发遍历目录计算实际使用空间
    fileSizeChan := make(chan int64, 100000)
    var wg sync.WaitGroup
    
    wg.Add(1)
    go walkDir(path, &wg, fileSizeChan)
    
    // 4. 解析目录名获取 K8s 元数据
    info.UID, info.Namespace, info.WorkLoad = processDirName(filepath.Base(path))
    
    return info, nil
}
```

## 关键设计模式

### 1. 平台抽象模式

**目的：** 实现跨平台支持，统一接口不同实现

**实现：**
- `stat_linux.go` / `stat_windows.go` - 平台特定的磁盘统计实现
- `type_linux.go` / `type_windows.go` - 平台特定的文件系统类型检测
- 使用 Go 的构建标签 (`//go:build`) 实现条件编译

**示例：**
```go
//go:build linux && !s390x && !arm && !386
// +build linux,!s390x,!arm,!386

package disk
// Linux 特定实现
```

### 2. 并发处理模式

**目的：** 提高性能，避免 I/O 阻塞

**实现策略：**

```go
// 信号量限制并发数，防止资源耗尽
var sem = make(chan struct{}, 100000)

// 协程池模式
go func(fileName string) {
    defer wg.Done()
    sem <- struct{}{}        // 获取信号量
    defer func() { <-sem }() // 释放信号量
    
    // 实际处理逻辑
    processFile(fileName)
}(file.Name())
```

**优势：**
- 控制最大并发数量，避免系统资源耗尽
- 提高 I/O 密集型操作的整体性能
- 使用 `sync.WaitGroup` 确保所有任务完成

### 3. Kubernetes 元数据解析

**目的：** 从 PVC 目录名中提取 K8s 资源信息

```go
func processDirName(dirName string) (uid, namespace, workload string) {
    // 解析 PVC 目录名格式: namespace-workload-pvc-uid
    parts := strings.SplitN(dirName, "-pvc-", 2)
    if len(parts) < 2 {
        return "notknown", "notknown", "notknown"
    }
    
    uid = parts[1]
    
    // 特殊处理 monitoring 命名空间
    if strings.Contains(parts[0], "monitoring") {
        part := strings.Split(parts[0], "-")
        namespace = part[0]
        workload = strings.Join(part[1:], "-")
        return uid, namespace, workload
    }
    
    // 通用解析逻辑
    parts = strings.Split(parts[0], "-")
    middleIndex := len(parts) / 2
    namespace = strings.Join(parts[:middleIndex], "-")
    workload = strings.Join(parts[middleIndex:], "-")
    
    return uid, namespace, workload
}
```

### 4. 配置注入模式

**全局配置管理：**
- 通过 `config.GlobalConfig` 管理应用配置
- 支持 YAML 配置文件和命令行参数
- 日志配置通过 `lumberjack` 实现自动轮转
- 日志初始化统一在 `config` 包中处理，避免重复初始化

**配置结构：**
```go
type Config struct {
    Cloud     string `yaml:"cloud"`
    Path      string `yaml:"path"`
    ClusterID string `yaml:"cluster_id"`
    ENV       string `yaml:"env"`
    OU        string `yaml:"ou"`
    K8sConfig string `yaml:"k8s_config"`
}
```

## 性能优化策略

### 1. 并发控制优化

```go
// 使用信号量限制最大并发数
var sem = make(chan struct{}, 100000)

// 分层并发处理
func walkDir(dirPath string, wg *sync.WaitGroup, fileSizeChan chan<- int64) {
    defer wg.Done()
    
    // 获取信号量，控制并发数
    sem <- struct{}{}
    defer func() { <-sem }()
    
    // 递归处理子目录
    var subWg sync.WaitGroup
    for _, entry := range entries {
        if entry.IsDir() {
            subWg.Add(1)
            go walkDir(path, &subWg, fileSizeChan)
        } else {
            subWg.Add(1)
            go calculateFileSize(path, fileSizeChan)
        }
    }
    subWg.Wait()
}
```

### 2. 内存管理优化

- **并发安全存储**：使用 `sync.Map` 实现无锁的并发安全结果存储
- **通道缓冲**：使用带缓冲的 channel 减少协程阻塞
- **资源复用**：复用协程和数据结构，减少 GC 压力

### 3. 系统调用优化

- **直接系统调用**：使用 `syscall.Statfs` 避免额外的系统调用开销
- **批量处理**：一次性获取文件系统信息，减少系统调用次数
- **平台特定优化**：针对不同平台使用最优的系统调用

### 4. I/O 优化

```go
// 使用带缓冲的通道收集文件大小
fileSizeChan := make(chan int64, 100000)

// 异步计算文件大小
go func() {
    var totalSize int64
    for size := range fileSizeChan {
        totalSize += size
    }
}()
```

## 监控指标设计

### 核心指标

每个监控的卷导出以下四个核心指标：

| 指标名称 | 类型 | 描述 |
|---------|------|------|
| `volume_bytes_total` | Gauge | 卷的总大小（字节） |
| `volume_bytes_free` | Gauge | 卷的可用空间（字节） |
| `volume_bytes_used` | Gauge | 卷的已用空间（字节） |
| `volume_percentage_used` | Gauge | 卷的使用率百分比 |

### 标签维度

每个指标包含以下标签维度：

| 标签名称 | 描述 | 示例值 |
|---------|------|--------|
| `cluster` | 集群名称 | `production-cluster` |
| `volume_path` | 卷的文件系统路径 | `/data/pvc-12345` |
| `namespace` | Kubernetes 命名空间 | `default`, `monitoring` |
| `workload` | 工作负载名称 | `nginx-deployment` |
| `uid` | PVC 的唯一标识符 | `12345-abcde-67890` |

### 指标示例

```prometheus
# HELP volume_bytes_total Total size of the volume/disk
# TYPE volume_bytes_total gauge
volume_bytes_total{cluster="prod",volume_path="/data/pvc-123",namespace="default",workload="nginx",uid="123-abc"} 1.073741824e+10

# HELP volume_bytes_free Free size of the volume/disk
# TYPE volume_bytes_free gauge
volume_bytes_free{cluster="prod",volume_path="/data/pvc-123",namespace="default",workload="nginx",uid="123-abc"} 5.36870912e+09

# HELP volume_bytes_used Used size of volume/disk
# TYPE volume_bytes_used gauge
volume_bytes_used{cluster="prod",volume_path="/data/pvc-123",namespace="default",workload="nginx",uid="123-abc"} 5.36870912e+09

# HELP volume_percentage_used Percentage of volume/disk Utilization
# TYPE volume_percentage_used gauge
volume_percentage_used{cluster="prod",volume_path="/data/pvc-123",namespace="default",workload="nginx",uid="123-abc"} 50.0
```

## 文件系统支持

### Linux 支持的文件系统

| 文件系统 | 十六进制标识 | 描述 |
|---------|-------------|------|
| EXT4 | ef53 | 主流 Linux 文件系统 |
| XFS | 58465342 | 高性能文件系统 |
| TMPFS | 1021994 | 内存文件系统 |
| NFS | 6969 | 网络文件系统 |
| ZFS | 2fc12fc1 | 高级文件系统 |
| NTFS | 5346544e | Windows 文件系统 |
| overlayfs | 794c7630 | 容器层文件系统 |

### Windows 支持

通过 Windows API 获取文件系统信息：
- `GetDiskFreeSpaceEx` - 获取磁盘空间信息
- `GetVolumeInformation` - 获取卷信息和文件系统类型

## 部署和使用

### 命令行参数

```bash
# 基本使用
./volume_exporter --volume-dir=logs:/var/log --volume-dir=data:/data

# 排除子目录
./volume_exporter --volume-dir=logs:/var/log --exclude-subdirs=temp,cache

# 自定义服务配置
./volume_exporter \
  --volume-dir=logs:/var/log \
  --web.listen-address=:9888 \
  --web.telemetry-path=/metrics
```

### Docker 部署

```bash
# 构建镜像
docker build -t volume_exporter .

# 运行容器
docker run -d \
  -p 9888:9888 \
  -v /var/log:/host/var/log:ro \
  volume_exporter \
  --volume-dir=logs:/host/var/log
```

### Kubernetes 边车模式

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: app-with-volume-exporter
spec:
  template:
    spec:
      containers:
      - name: app
        image: nginx
        volumeMounts:
        - name: data-volume
          mountPath: /data
      
      - name: volume-exporter
        image: volume_exporter
        args:
        - --volume-dir=data:/data
        ports:
        - containerPort: 9888
          name: metrics
        volumeMounts:
        - name: data-volume
          mountPath: /data
          readOnly: true
      
      volumes:
      - name: data-volume
        persistentVolumeClaim:
          claimName: app-data-pvc
```

## 总结

Volume Exporter 的架构设计充分体现了以下设计原则：

1. **关注点分离**：每个模块专注特定功能，职责清晰
2. **平台抽象**：统一接口，平台特定实现
3. **高并发处理**：充分利用 Go 协程和通道特性
4. **云原生设计**：专为 Kubernetes 环境优化
5. **性能优先**：多层次的性能优化策略

这使得它非常适合在资源受限的容器化环境中作为轻量级监控解决方案使用，特别是在传统节点导出器无法部署的场景下。