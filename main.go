package main

import (
	"flag"
	"fmt"
	"net/http"
	"os"
	"strings"

	"github.com/golang/glog"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/prometheus/common/version"

	"github.com/mnadeem/volume_exporter/exporter"
)

type volumeNamePathFlag []string

func (v *volumeNamePathFlag) Set(value string) error {
	*v = append(*v, value)
	return nil
}

func (v *volumeNamePathFlag) String() string {
	return fmt.Sprint(*v)
}

type volumeFlags struct {
	volumeNamePath volumeNamePathFlag
	excludeSubdirs string
}

func main() {
	// 确保日志立即输出到标准输出
	flag.Set("logtostderr", "true")
	flag.Set("v", "2")
	flag.Set("alsologtostderr", "true")

	var (
		listenAddress = flag.String("web.listen-address", ":9888", "Address to listen on for web interface and telemetry.")
		metricPath    = flag.String("web.telemetry-path", "/metrics", "Path under which to expose metrics.")

		volFlags = volumeFlags{}
	)

	glog.Infoln("Starting volume_exporter")
	glog.Flush() // 确保日志立即输出

	flag.Var(&volFlags.volumeNamePath, "volume-dir", "Volumes to report, the format is volumeName:VolumeDir;\n For example ==> logs:/app/logs; can be used multiple times to provide more than one value")
	flag.StringVar(&volFlags.excludeSubdirs, "exclude-subdirs", "", "Comma-separated list of subdirectories to exclude from monitoring (only applies to first-level subdirectories)")
	flag.Parse()

	if len(volFlags.volumeNamePath) < 1 {
		glog.Infoln("Missing volume-dir")
		flag.Usage()
		os.Exit(1)
	}

	volOpts := exporter.VolumeOpts{}
	//var wg sync.WaitGroup

	for _, np := range volFlags.volumeNamePath {
		name, path := splitFlag(np)
		glog.Infof("监控集群 : %s, 监控路经 : %s\n", name, path)

		// 处理排除目录
		var excludeDirs []string
		if volFlags.excludeSubdirs != "" {
			excludeDirs = strings.Split(volFlags.excludeSubdirs, ",")
			for i, dir := range excludeDirs {
				excludeDirs[i] = strings.TrimSpace(dir)
			}
		}

		volOpts.Options = append(volOpts.Options, exporter.VolumeOpt{
			Name:        name,
			Path:        path,
			ExcludeDirs: excludeDirs,
		})
		//go disk.ProcessSubdirectories(path, &wg, disk.DirSizes)
	}

	// 移除并发管理器初始化以提高性能
	exporter.Register(&volOpts)

	glog.Infoln("Starting volume_exporter", version.Info())
	glog.Infoln("Build context", version.BuildContext())

	glog.Fatal(serverMetrics(*listenAddress, *metricPath))
}

// Split colon separated flag into two fields
func splitFlag(s string) (string, string) {

	if len(s) == 0 {
		glog.Fatalln("Nothing to Monitor")
		os.Exit(1)
	}

	slice := strings.SplitN(s, ":", 2)

	if len(slice) == 1 {
		glog.Fatalf("Invalid option %s\n", s)
		os.Exit(1)
	}
	name := strings.TrimSpace(slice[0])
	path := strings.TrimSpace(slice[1])

	if len(name) == 0 {
		glog.Fatalf("Invalid Name on %s\n", s)
		os.Exit(1)
	}

	exists, err := exists(path)
	if err != nil {
		glog.Fatalf("Error validating if path exists %s , error %v\n", path, err)
		os.Exit(1)
	}
	if !exists {
		glog.Fatalf("Invalid Path %s\n", path)
		os.Exit(1)
	}

	return name, path
}

func exists(path string) (bool, error) {
	stat, err := os.Stat(path)
	if err == nil {
		return true, nil
	}
	if stat.IsDir() {
		return true, nil
	}
	return false, err
}

func serverMetrics(listenAddress, metricsPath string) error {
	http.Handle(metricsPath, promhttp.Handler())
	http.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		w.Write([]byte(`
			<html>
			<head><title>Volume Exporter Metrics</title></head>
			<body>
			<h1>Volume Exporter Metrics</h1>
			<p><a href='` + metricsPath + `'>Metrics</a></p>
			</body>
			</html>
		`))
	})

	glog.Infof("Starting Server: %s,path: %s\n", listenAddress, metricsPath)
	return http.ListenAndServe(listenAddress, nil)
}
