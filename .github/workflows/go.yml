# This workflow will build a golang project
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-go

name: Go

on:
  push:
    branches: [ "master" ]
  pull_request:
    branches: [ "master" ]

jobs:

  build:
    runs-on: ubuntu-latest
    environment: GO
    strategy:
      matrix:
        go-version: [ '1.21.x' ]
    steps:
    - uses: actions/checkout@v3

    - name: Set up Go ${{ matrix.go-version }}
      uses: actions/setup-go@v4
      with:
        go-version: ${{ matrix.go-version }}
    - name: Display Go version
      run: go version
    - name: setup env
      run: |
        echo "$GOBIN $(go env GOPATH)"
        CGO_ENABLED=1
      shell: bash
    - name: Install dependencies
      run: |
        go get .
        go get github.com/prometheus/client_golang@v1.16.0
        go get github.com/prometheus/common@v0.44.0
        go get golang.org/x/sys@v0.12.0
    - name: Build
      run: |
        go build -o volume_exporter main.go
        ls -R
    - name: Login to Docker Hub
      uses: docker/login-action@v3
      with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    - name: Build and push
      uses: docker/build-push-action@v5
      with:
          context: .
          file: ./Dockerfile
          push: true
          tags: ${{ secrets.DOCKERHUB_USERNAME }}/volume_exporter:latest
