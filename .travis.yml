sudo: required

# https://docs.travis-ci.com/user/reference/overview/
# https://docs.travis-ci.com/user/multi-os/

dist: trusty         # or bionic | xenial | trusty | precise with xenial as default
arch: amd64          # optional, this is default, routes to a full VM
os:
  - linux

services:
- docker

language: go

go:
- 1.15

go_import_path: github.com/mnadeem/volume_exporter

before_script:
  - docker --version

script:
  - make

after_success:
- if [ "$TRAVIS_PULL_REQUEST" == "false" ]; then
  make docker;
  make push;
  fi
- if [[ -n "$TRAVIS_TAG" ]]; then
  make crossbuild release;
  fi